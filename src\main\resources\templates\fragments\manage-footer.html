<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
</head>
<body>
    <!-- 관리자 푸터 Fragment -->
    <footer th:fragment="footer" class="footer">
        <div class="container">
            <div class="row">
                <!-- 회사 정보 -->
                <div class="col-md-4">
                    <h5>
                        <img src="/images/logo-white.png" alt="한림공원" height="30">
                        한림공원 관리시스템
                    </h5>
                    <p>
                        한림공원 QR 체험 서비스의<br>
                        통합 관리 시스템
                    </p>
                </div>
                
                <!-- 관리 메뉴 -->
                <div class="col-md-3">
                    <h6>관리 메뉴</h6>
                    <ul>
                        <li>
                            <a th:href="@{/manage}">대시보드</a>
                        </li>
                        <li>
                            <a href="#">사용자 관리</a>
                        </li>
                        <li>
                            <a th:href="@{/manage/quiz/list}">콘텐츠 관리</a>
                        </li>
                        <li>
                            <a href="#">QR 코드 관리</a>
                        </li>
                    </ul>
                </div>

                <!-- 시스템 정보 -->
                <div class="col-md-2">
                    <h6>시스템</h6>
                    <ul>
                        <li>
                            <a href="#">시스템 상태</a>
                        </li>
                        <li>
                            <a href="#">백업 관리</a>
                        </li>
                        <li>
                            <a href="#">로그 조회</a>
                        </li>
                    </ul>
                </div>
                
                <!-- 연락처 정보 -->
                <div class="col-md-3">
                    <h6>연락처</h6>
                    <div class="contact-info">
                        <div>
                            <i class="fas fa-map-marker-alt"></i>
                            제주특별자치도 한림읍
                        </div>
                        <div>
                            <i class="fas fa-phone"></i>
                            <a href="tel:************">************</a>
                        </div>
                        <div>
                            <i class="fas fa-envelope"></i>
                            <a href="mailto:<EMAIL>"><EMAIL></a>
                        </div>
                        <div>
                            <i class="fas fa-clock"></i>
                            09:00 - 18:00 (연중무휴)
                        </div>
                    </div>
                </div>
            </div>

            <hr>

            <!-- 저작권 정보 -->
            <div class="row">
                <div class="col-md-6">
                    <p>
                        &copy; <span th:text="${#dates.year(#dates.createNow())}">2024</span> 한림공원. All rights reserved.
                    </p>
                </div>
                <div class="col-md-6">
                    <p>
                        Powered by <a href="https://wayplus.kr">WayPlus</a>
                    </p>
                </div>
            </div>
        </div>
        
        <!-- 맨 위로 버튼 -->
        <button type="button" class="btn btn-primary btn-floating position-fixed bottom-0 end-0 m-3" 
                id="scrollToTopBtn" style="display: none; z-index: 1000;" title="맨 위로">
            <i class="fas fa-chevron-up"></i>
        </button>
    </footer>
    
    <!-- 맨 위로 버튼 스크립트 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const scrollToTopBtn = document.getElementById('scrollToTopBtn');
            
            // 스크롤 이벤트 리스너
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    scrollToTopBtn.style.display = 'block';
                } else {
                    scrollToTopBtn.style.display = 'none';
                }
            });
            
            // 클릭 이벤트 리스너
            scrollToTopBtn.addEventListener('click', function() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>
