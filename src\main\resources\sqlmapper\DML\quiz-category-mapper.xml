<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kr.wayplus.qr_hallimpark.mapper.QuizCategoryMapper">

    <!-- 문제 카테고리 ResultMap -->
    <resultMap id="QuizCategoryResultMap" type="kr.wayplus.qr_hallimpark.model.QuizCategory">
        <id property="categoryId" column="category_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="categoryName" column="category_name"/>
        <result property="description" column="description"/>
        <result property="createId" column="create_id"/>
        <result property="createDate" column="create_date"/>
        <result property="lastUpdateId" column="last_update_id"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="deleteYn" column="delete_yn"/>
        <result property="deleteId" column="delete_id"/>
        <result property="deleteDate" column="delete_date"/>
    </resultMap>

    <!-- 계층형 구조 포함 ResultMap -->
    <resultMap id="QuizCategoryHierarchyResultMap" type="kr.wayplus.qr_hallimpark.model.QuizCategory">
        <id property="categoryId" column="category_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="categoryName" column="category_name"/>
        <result property="description" column="description"/>
        <result property="createId" column="create_id"/>
        <result property="createDate" column="create_date"/>
        <result property="lastUpdateId" column="last_update_id"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="deleteYn" column="delete_yn"/>
        <result property="deleteId" column="delete_id"/>
        <result property="deleteDate" column="delete_date"/>
        <!-- 계층형 구조 관련 필드 -->
        <result property="parentCategoryName" column="parent_category_name"/>
        <result property="depth" column="depth"/>
        <result property="categoryPath" column="category_path"/>
        <result property="childrenCount" column="children_count"/>
    </resultMap>

    <!-- 모든 문제 카테고리 목록 조회 -->
    <select id="selectQuizCategoryList" resultMap="QuizCategoryResultMap">
        SELECT
            category_id,
            parent_id,
            category_name,
            description,
            create_id,
            create_date,
            last_update_id,
            last_update_date,
            delete_yn,
            delete_id,
            delete_date
        FROM quiz_category
        WHERE delete_yn = 'N'
        ORDER BY category_id DESC
    </select>

    <!-- 카테고리 ID로 문제 카테고리 조회 -->
    <select id="selectQuizCategoryById" parameterType="long" resultMap="QuizCategoryResultMap">
        SELECT
            category_id,
            parent_id,
            category_name,
            description,
            create_id,
            create_date,
            last_update_id,
            last_update_date,
            delete_yn,
            delete_id,
            delete_date
        FROM quiz_category
        WHERE category_id = #{categoryId}
        AND delete_yn = 'N'
    </select>

    <!-- 카테고리명으로 문제 카테고리 조회 -->
    <select id="selectQuizCategoryByName" parameterType="string" resultMap="QuizCategoryResultMap">
        SELECT
            category_id,
            parent_id,
            category_name,
            description,
            create_id,
            create_date,
            last_update_id,
            last_update_date,
            delete_yn,
            delete_id,
            delete_date
        FROM quiz_category
        WHERE category_name = #{categoryName}
        AND delete_yn = 'N'
    </select>

    <!-- 문제 카테고리 등록 -->
    <insert id="insertQuizCategory" parameterType="kr.wayplus.qr_hallimpark.model.QuizCategory" useGeneratedKeys="true" keyProperty="categoryId">
        INSERT INTO quiz_category (
            parent_id,
            category_name,
            description,
            create_id,
            delete_yn
        ) VALUES (
            #{parentId},
            #{categoryName},
            #{description},
            #{createId},
            'N'
        )
    </insert>

    <!-- 문제 카테고리 수정 -->
    <update id="updateQuizCategory" parameterType="kr.wayplus.qr_hallimpark.model.QuizCategory">
        UPDATE quiz_category
        SET
            parent_id = #{parentId},
            category_name = #{categoryName},
            description = #{description},
            last_update_id = #{lastUpdateId}
        WHERE category_id = #{categoryId}
        AND delete_yn = 'N'
    </update>

    <!-- 문제 카테고리 삭제 (소프트 삭제) -->
    <update id="deleteQuizCategory" parameterType="kr.wayplus.qr_hallimpark.model.QuizCategory">
        UPDATE quiz_category
        SET
            category_name = CONCAT(category_name, '_DELETED_', UNIX_TIMESTAMP()),
            delete_yn = 'Y',
            delete_id = #{deleteId},
            delete_date = NOW()
        WHERE category_id = #{categoryId}
        AND delete_yn = 'N'
    </update>

    <!-- 카테고리명 중복 체크 -->
    <select id="countByCategoryName" parameterType="string" resultType="int">
        SELECT COUNT(*)
        FROM quiz_category
        WHERE category_name = #{categoryName}
        AND delete_yn = 'N'
    </select>

    <!-- 카테고리명 중복 체크 (수정 시 자기 자신 제외) -->
    <select id="countByCategoryNameExcludeId" resultType="int">
        SELECT COUNT(*)
        FROM quiz_category
        WHERE category_name = #{categoryName}
        AND category_id != #{categoryId}
        AND delete_yn = 'N'
    </select>

    <!-- 공통 동적 검색 조건을 사용한 카테고리 목록 조회 -->
    <select id="selectListWithConditions" parameterType="AdminListSearch" resultMap="QuizCategoryHierarchyResultMap">
        SELECT
            c.category_id,
            c.parent_id,
            c.category_name,
            c.description,
            c.create_id,
            c.create_date,
            c.last_update_id,
            c.last_update_date,
            c.delete_yn,
            c.delete_id,
            c.delete_date,
            p.category_name AS parent_category_name,
            (SELECT COUNT(*) FROM quiz_category child WHERE child.parent_id = c.category_id AND child.delete_yn = 'N') AS children_count
        FROM quiz_category c
        LEFT JOIN quiz_category p ON c.parent_id = p.category_id AND p.delete_yn = 'N'
        <where>
            <!-- 삭제 여부 조건 -->
            <if test="includeDeleted == null or includeDeleted == false">
                c.delete_yn = 'N'
            </if>

            <!-- 부모 ID 조건 -->
            <if test="parentId != null">
                AND c.parent_id = #{parentId}
            </if>

            <!-- 카테고리 ID 조건 (단일) -->
            <if test="categoryId != null">
                AND c.category_id = #{categoryId}
            </if>

            <!-- 카테고리 ID 조건 (다중) -->
            <if test="categoryIds != null and !categoryIds.isEmpty()">
                AND c.category_id IN
                <foreach collection="categoryIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>

            <!-- 검색 키워드 조건 -->
            <if test="searchKeyword != null and searchKeyword != '' and searchFields != null and !searchFields.isEmpty()">
                AND (
                <foreach collection="searchFields" item="field" separator=" OR ">
                    c.${field} LIKE CONCAT('%', #{searchKeyword}, '%')
                </foreach>
                )
            </if>

            <!-- 기존 필터 조건들 (하위 호환성을 위해 유지) -->
            <if test="filters != null and !filters.isEmpty()">
                <foreach collection="filters" index="key" item="value">
                    <if test="value != null and value != ''">
                        AND c.${key} = #{value}
                    </if>
                </foreach>
            </if>

            <!-- 날짜 범위 조건 -->
            <if test="hasDateRange()">
                AND c.${dateField} BETWEEN #{dateFrom} AND #{dateTo}
            </if>

            <!-- 숫자 범위 조건 -->
            <if test="hasNumberRange()">
                AND c.${numberField} BETWEEN #{numberFrom} AND #{numberTo}
            </if>
        </where>
        <if test="sortField != null and sortField != '' and sortDirection != null and (sortDirection == 'ASC' or sortDirection == 'DESC')">
            ORDER BY c.${sortField} ${sortDirection}
        </if>
        <if test="sortField == null or sortField == '' or sortDirection == null or (sortDirection != 'ASC' and sortDirection != 'DESC')">
            ORDER BY
                CASE WHEN c.parent_id IS NULL THEN c.category_id ELSE c.parent_id END DESC,
                c.parent_id ASC,
                c.category_id DESC
        </if>
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 공통 동적 검색 조건을 사용한 카테고리 개수 조회 -->
    <select id="countWithConditions" parameterType="AdminListSearch" resultType="long">
        SELECT COUNT(*)
        FROM quiz_category c
        LEFT JOIN quiz_category p ON c.parent_id = p.category_id AND p.delete_yn = 'N'
        <where>
            <!-- 삭제 여부 조건 -->
            <if test="includeDeleted == null or includeDeleted == false">
                c.delete_yn = 'N'
            </if>

            <!-- 부모 ID 조건 -->
            <if test="parentId != null">
                AND c.parent_id = #{parentId}
            </if>

            <!-- 카테고리 ID 조건 (단일) -->
            <if test="categoryId != null">
                AND c.category_id = #{categoryId}
            </if>

            <!-- 카테고리 ID 조건 (다중) -->
            <if test="categoryIds != null and !categoryIds.isEmpty()">
                AND c.category_id IN
                <foreach collection="categoryIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>

            <!-- 검색 키워드 조건 -->
            <if test="searchKeyword != null and searchKeyword != '' and searchFields != null and !searchFields.isEmpty()">
                AND (
                <foreach collection="searchFields" item="field" separator=" OR ">
                    c.${field} LIKE CONCAT('%', #{searchKeyword}, '%')
                </foreach>
                )
            </if>

            <!-- 기존 필터 조건들 (하위 호환성을 위해 유지) -->
            <if test="filters != null and !filters.isEmpty()">
                <foreach collection="filters" index="key" item="value">
                    <if test="value != null and value != ''">
                        AND c.${key} = #{value}
                    </if>
                </foreach>
            </if>
            <!-- 날짜 범위 조건 -->
            <if test="hasDateRange()">
                AND c.${dateField} BETWEEN #{dateFrom} AND #{dateTo}
            </if>

            <!-- 숫자 범위 조건 -->
            <if test="hasNumberRange()">
                AND c.${numberField} BETWEEN #{numberFrom} AND #{numberTo}
            </if>
        </where>
    </select>

    <!-- ========== 계층형 구조 관련 쿼리 ========== -->

    <!-- 최상위 카테고리 목록 조회 -->
    <select id="selectRootCategories" resultMap="QuizCategoryResultMap">
        SELECT
            category_id,
            parent_id,
            category_name,
            description,
            create_id,
            create_date,
            last_update_id,
            last_update_date,
            delete_yn,
            delete_id,
            delete_date
        FROM quiz_category
        WHERE parent_id IS NULL
        AND delete_yn = 'N'
        ORDER BY category_id DESC
    </select>

    <!-- 특정 부모 카테고리의 자식 카테고리 목록 조회 -->
    <select id="selectChildCategories" parameterType="long" resultMap="QuizCategoryResultMap">
        SELECT
            category_id,
            parent_id,
            category_name,
            description,
            create_id,
            create_date,
            last_update_id,
            last_update_date,
            delete_yn,
            delete_id,
            delete_date
        FROM quiz_category
        WHERE parent_id = #{parentId}
        AND delete_yn = 'N'
        ORDER BY category_id DESC
    </select>

    <!-- 계층형 구조로 모든 카테고리 조회 (부모 카테고리명 포함) -->
    <select id="selectCategoriesWithHierarchy" resultMap="QuizCategoryHierarchyResultMap">
        SELECT
            c.category_id,
            c.parent_id,
            c.category_name,
            c.description,
            c.create_id,
            c.create_date,
            c.last_update_id,
            c.last_update_date,
            c.delete_yn,
            c.delete_id,
            c.delete_date,
            p.category_name AS parent_category_name,
            (SELECT COUNT(*) FROM quiz_category child WHERE child.parent_id = c.category_id AND child.delete_yn = 'N') AS children_count
        FROM quiz_category c
        LEFT JOIN quiz_category p ON c.parent_id = p.category_id AND p.delete_yn = 'N'
        WHERE c.delete_yn = 'N'
        ORDER BY
            CASE WHEN c.parent_id IS NULL THEN c.category_id ELSE c.parent_id END DESC,
            c.parent_id ASC,
            c.category_id DESC
    </select>

    <!-- 특정 카테고리의 모든 하위 카테고리 ID 목록 조회 (재귀적) -->
    <select id="selectDescendantCategoryIds" parameterType="long" resultType="long">
        WITH RECURSIVE descendant_categories AS (
            SELECT category_id
            FROM quiz_category
            WHERE parent_id = #{categoryId}
            AND delete_yn = 'N'

            UNION ALL
            
            SELECT c.category_id
            FROM quiz_category c
            INNER JOIN descendant_categories dc ON c.parent_id = dc.category_id
            WHERE c.delete_yn = 'N'
        )
        SELECT category_id FROM descendant_categories
    </select>

    <!-- 특정 카테고리의 상위 카테고리 경로 조회 -->
    <select id="selectCategoryPath" parameterType="long" resultMap="QuizCategoryResultMap">
        WITH RECURSIVE category_path AS (
            SELECT category_id, parent_id, category_name, 0 as level
            FROM quiz_category
            WHERE category_id = #{categoryId}
            AND delete_yn = 'N'

            UNION ALL

            SELECT c.category_id, c.parent_id, c.category_name, cp.level + 1
            FROM quiz_category c
            INNER JOIN category_path cp ON c.category_id = cp.parent_id
            WHERE c.delete_yn = 'N'
        )
        SELECT category_id, parent_id, category_name
        FROM category_path
        ORDER BY level DESC
    </select>

    <!-- 부모 카테고리 ID로 자식 카테고리 개수 조회 -->
    <select id="countChildCategories" parameterType="long" resultType="int">
        SELECT COUNT(*)
        FROM quiz_category
        WHERE
            <choose>
                <when test="parentId == null">
                    parent_id IS NULL
                </when>
                <otherwise>
                    parent_id = #{parentId}
                </otherwise>
            </choose>
        AND delete_yn = 'N'
    </select>

    <!-- 카테고리 ID로 깊이(depth) 조회 -->
    <select id="selectCategoryDepth" parameterType="long" resultType="int">
        WITH RECURSIVE category_depth AS (
            SELECT category_id, parent_id, 0 as depth
            FROM quiz_category
            WHERE category_id = #{categoryId}
            AND delete_yn = 'N'

            UNION ALL

            SELECT c.category_id, c.parent_id, cd.depth + 1
            FROM quiz_category c
            INNER JOIN category_depth cd ON c.category_id = cd.parent_id
            WHERE c.delete_yn = 'N'
        )
        SELECT MAX(depth) FROM category_depth
    </select>

    <!-- 순환 참조 체크를 위한 상위 카테고리 ID 목록 조회 -->
    <select id="selectAncestorCategoryIds" parameterType="long" resultType="long">
        WITH RECURSIVE ancestor_categories AS (
            SELECT category_id, parent_id
            FROM quiz_category
            WHERE category_id = #{categoryId}
            AND delete_yn = 'N'

            UNION ALL
            
            SELECT c.category_id, c.parent_id
            FROM quiz_category c
            INNER JOIN ancestor_categories ac ON c.category_id = ac.parent_id
            WHERE c.delete_yn = 'N'
        )
        SELECT category_id FROM ancestor_categories WHERE category_id != #{categoryId}
    </select>

    <!-- 부모 카테고리 선택용 목록 조회 (특정 카테고리와 그 하위 카테고리 제외) -->
    <select id="selectParentCategoryOptions" parameterType="long" resultMap="QuizCategoryHierarchyResultMap">
        SELECT
            c.category_id,
            c.parent_id,
            c.category_name,
            c.description,
            c.create_id,
            c.create_date,
            c.last_update_id,
            c.last_update_date,
            c.delete_yn,
            c.delete_id,
            c.delete_date,
            p.category_name AS parent_category_name
        FROM quiz_category c
        LEFT JOIN quiz_category p ON c.parent_id = p.category_id AND p.delete_yn = 'N'
        WHERE c.delete_yn = 'N'
        <if test="excludeCategoryId != null">
            AND c.category_id != #{excludeCategoryId}
            AND c.category_id NOT IN (
                WITH RECURSIVE descendant_categories AS (
                    SELECT category_id
                    FROM quiz_category
                    WHERE parent_id = #{excludeCategoryId}
                    AND delete_yn = 'N'

                    UNION ALL

                    SELECT c2.category_id
                    FROM quiz_category c2
                    INNER JOIN descendant_categories dc ON c2.parent_id = dc.category_id
                    WHERE c2.delete_yn = 'N'
                )
                SELECT category_id FROM descendant_categories
            )
        </if>
        ORDER BY
            CASE WHEN c.parent_id IS NULL THEN c.category_id ELSE c.parent_id END DESC,
            c.parent_id ASC,
            c.category_id DESC
    </select>

</mapper>
