<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kr.wayplus.qr_hallimpark.common.mapper.AdminListMapper">

    <!-- 공통 동적 검색 조건 SQL Fragment -->
    <sql id="commonSearchConditions">
        <where>
            <!-- 삭제 여부 조건 -->
            <if test="includeDeleted == null or includeDeleted == false">
                delete_yn = 'N'
            </if>

            <!-- 상태 조건 -->
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>

            <!-- 카테고리 ID 조건 (단일) -->
            <if test="categoryId != null">
                AND category_id = #{categoryId}
            </if>

            <!-- 카테고리 ID 조건 (다중) -->
            <if test="categoryIds != null and !categoryIds.isEmpty()">
                AND category_id IN
                <foreach collection="categoryIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>

            <!-- 사용자 ID 조건 -->
            <if test="userId != null and userId != ''">
                AND create_id = #{userId}
            </if>

            <!-- 부모 ID 조건 -->
            <if test="parentId != null">
                AND parent_id = #{parentId}
            </if>

            <!-- 활성화 여부 조건 -->
            <if test="isActive != null">
                <choose>
                    <when test="isActive == true">
                        AND status = 'ACTIVE'
                    </when>
                    <when test="isActive == false">
                        AND status = 'INACTIVE'
                    </when>
                </choose>
            </if>

            <!-- 검색 키워드 조건 -->
            <if test="searchKeyword != null and searchKeyword != '' and searchFields != null and !searchFields.isEmpty()">
                AND (
                <foreach collection="searchFields" item="field" separator=" OR ">
                    ${field} LIKE CONCAT('%', #{searchKeyword}, '%')
                </foreach>
                )
            </if>

            <!-- 기존 필터 조건들 (하위 호환성을 위해 유지) -->
            <if test="filters != null and !filters.isEmpty()">
                <foreach collection="filters" index="key" item="value">
                    <if test="value != null and value != ''">
                        AND ${key} = #{value}
                    </if>
                </foreach>
            </if>

            <!-- 날짜 범위 조건 -->
            <if test="hasDateRange()">
                AND ${dateField} BETWEEN #{dateFrom} AND #{dateTo}
            </if>

            <!-- 숫자 범위 조건 -->
            <if test="hasNumberRange()">
                AND ${numberField} BETWEEN #{numberFrom} AND #{numberTo}
            </if>
        </where>
    </sql>
    
    <!-- 공통 정렬 조건 SQL Fragment -->
    <sql id="commonOrderBy">
        <if test="sortField != null and sortField != '' and sortDirection != null and (sortDirection == 'ASC' or sortDirection == 'DESC')">
            ORDER BY ${sortField} ${sortDirection}
        </if>
        <if test="sortField == null or sortField == '' or sortDirection == null or (sortDirection != 'ASC' and sortDirection != 'DESC')">
            ORDER BY create_date DESC
        </if>
    </sql>
    
    <!-- 공통 페이징 조건 SQL Fragment -->
    <sql id="commonPaging">
        LIMIT #{offset}, #{limit}
    </sql>
    
    <!-- 동적 리스트 조회 템플릿 -->
    <select id="selectListWithConditions" parameterType="AdminListSearch" resultType="map">
        SELECT *
        FROM ${tableName}
        <include refid="commonSearchConditions"/>
        <include refid="commonOrderBy"/>
        <include refid="commonPaging"/>
    </select>
    
    <!-- 동적 개수 조회 템플릿 -->
    <select id="countWithConditions" parameterType="AdminListSearch" resultType="long">
        SELECT COUNT(*)
        FROM ${tableName}
        <include refid="commonSearchConditions"/>
    </select>
    
    <!-- 특정 테이블용 동적 리스트 조회 (테이블명을 파라미터로 받지 않는 버전) -->
    <select id="selectListWithConditionsForTable" parameterType="map" resultType="map">
        SELECT *
        FROM ${tableName}
        <where>
            <!-- 삭제 여부 조건 -->
            <if test="searchCondition.includeDeleted == null or searchCondition.includeDeleted == false">
                delete_yn = 'N'
            </if>

            <!-- 상태 조건 -->
            <if test="searchCondition.status != null and searchCondition.status != ''">
                AND status = #{searchCondition.status}
            </if>

            <!-- 카테고리 ID 조건 (단일) -->
            <if test="searchCondition.categoryId != null">
                AND category_id = #{searchCondition.categoryId}
            </if>

            <!-- 카테고리 ID 조건 (다중) -->
            <if test="searchCondition.categoryIds != null and !searchCondition.categoryIds.isEmpty()">
                AND category_id IN
                <foreach collection="searchCondition.categoryIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>

            <!-- 사용자 ID 조건 -->
            <if test="searchCondition.userId != null and searchCondition.userId != ''">
                AND create_id = #{searchCondition.userId}
            </if>

            <!-- 부모 ID 조건 -->
            <if test="searchCondition.parentId != null">
                AND parent_id = #{searchCondition.parentId}
            </if>

            <!-- 활성화 여부 조건 -->
            <if test="searchCondition.isActive != null">
                <choose>
                    <when test="searchCondition.isActive == true">
                        AND status = 'ACTIVE'
                    </when>
                    <when test="searchCondition.isActive == false">
                        AND status = 'INACTIVE'
                    </when>
                </choose>
            </if>

            <!-- 검색 키워드 조건 -->
            <if test="searchCondition.searchKeyword != null and searchCondition.searchKeyword != '' and searchCondition.searchFields != null and !searchCondition.searchFields.isEmpty()">
                AND (
                <foreach collection="searchCondition.searchFields" item="field" separator=" OR ">
                    ${field} LIKE CONCAT('%', #{searchCondition.searchKeyword}, '%')
                </foreach>
                )
            </if>

            <!-- 기존 필터 조건들 (하위 호환성을 위해 유지) -->
            <if test="searchCondition.filters != null and !searchCondition.filters.isEmpty()">
                <foreach collection="searchCondition.filters" index="key" item="value">
                    <if test="value != null and value != ''">
                        AND ${key} = #{value}
                    </if>
                </foreach>
            </if>

            <!-- 날짜 범위 조건 -->
            <if test="searchCondition.hasDateRange()">
                AND ${searchCondition.dateField} BETWEEN #{searchCondition.dateFrom} AND #{searchCondition.dateTo}
            </if>

            <!-- 숫자 범위 조건 -->
            <if test="searchCondition.hasNumberRange()">
                AND ${searchCondition.numberField} BETWEEN #{searchCondition.numberFrom} AND #{searchCondition.numberTo}
            </if>
        </where>
        <if test="searchCondition.sortField != null and searchCondition.sortField != '' and searchCondition.sortDirection != null and (searchCondition.sortDirection == 'ASC' or searchCondition.sortDirection == 'DESC')">
            ORDER BY ${searchCondition.sortField} ${searchCondition.sortDirection}
        </if>
        <if test="searchCondition.sortField == null or searchCondition.sortField == '' or searchCondition.sortDirection == null or (searchCondition.sortDirection != 'ASC' and searchCondition.sortDirection != 'DESC')">
            ORDER BY create_date DESC
        </if>
        LIMIT #{searchCondition.offset}, #{searchCondition.limit}
    </select>
    
    <!-- 특정 테이블용 동적 개수 조회 -->
    <select id="countWithConditionsForTable" parameterType="map" resultType="long">
        SELECT COUNT(*)
        FROM ${tableName}
        <where>
            <!-- 삭제 여부 조건 -->
            <if test="searchCondition.includeDeleted == null or searchCondition.includeDeleted == false">
                delete_yn = 'N'
            </if>

            <!-- 상태 조건 -->
            <if test="searchCondition.status != null and searchCondition.status != ''">
                AND status = #{searchCondition.status}
            </if>

            <!-- 카테고리 ID 조건 (단일) -->
            <if test="searchCondition.categoryId != null">
                AND category_id = #{searchCondition.categoryId}
            </if>

            <!-- 카테고리 ID 조건 (다중) -->
            <if test="searchCondition.categoryIds != null and !searchCondition.categoryIds.isEmpty()">
                AND category_id IN
                <foreach collection="searchCondition.categoryIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>

            <!-- 사용자 ID 조건 -->
            <if test="searchCondition.userId != null and searchCondition.userId != ''">
                AND create_id = #{searchCondition.userId}
            </if>

            <!-- 부모 ID 조건 -->
            <if test="searchCondition.parentId != null">
                AND parent_id = #{searchCondition.parentId}
            </if>

            <!-- 활성화 여부 조건 -->
            <if test="searchCondition.isActive != null">
                <choose>
                    <when test="searchCondition.isActive == true">
                        AND status = 'ACTIVE'
                    </when>
                    <when test="searchCondition.isActive == false">
                        AND status = 'INACTIVE'
                    </when>
                </choose>
            </if>

            <!-- 검색 키워드 조건 -->
            <if test="searchCondition.searchKeyword != null and searchCondition.searchKeyword != '' and searchCondition.searchFields != null and !searchCondition.searchFields.isEmpty()">
                AND (
                <foreach collection="searchCondition.searchFields" item="field" separator=" OR ">
                    ${field} LIKE CONCAT('%', #{searchCondition.searchKeyword}, '%')
                </foreach>
                )
            </if>

            <!-- 기존 필터 조건들 (하위 호환성을 위해 유지) -->
            <if test="searchCondition.filters != null and !searchCondition.filters.isEmpty()">
                <foreach collection="searchCondition.filters" index="key" item="value">
                    <if test="value != null and value != ''">
                        AND ${key} = #{value}
                    </if>
                </foreach>
            </if>

            <!-- 날짜 범위 조건 -->
            <if test="searchCondition.hasDateRange()">
                AND ${searchCondition.dateField} BETWEEN #{searchCondition.dateFrom} AND #{searchCondition.dateTo}
            </if>

            <!-- 숫자 범위 조건 -->
            <if test="searchCondition.hasNumberRange()">
                AND ${searchCondition.numberField} BETWEEN #{searchCondition.numberFrom} AND #{searchCondition.numberTo}
            </if>
        </where>
    </select>

</mapper>
