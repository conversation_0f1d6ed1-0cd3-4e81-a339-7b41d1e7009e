package kr.wayplus.qr_hallimpark.controller.manage;

import kr.wayplus.qr_hallimpark.common.utils.AdminListUtils;
import kr.wayplus.qr_hallimpark.model.AdminListResponse;
import kr.wayplus.qr_hallimpark.model.AdminListSearch;
import kr.wayplus.qr_hallimpark.model.QuizCategory;
import kr.wayplus.qr_hallimpark.service.QuizCategoryService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpServletRequest;
import java.sql.SQLIntegrityConstraintViolationException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * 관리자 문제 카테고리 컨트롤러
 * - 문제 카테고리 관리 페이지 및 API 처리
 */
@Controller
@RequestMapping("/manage/quiz-category")
@RequiredArgsConstructor
public class ManageQuizCategoryController {
    
    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final QuizCategoryService quizCategoryService;
    
    /**
     * 문제 카테고리 목록 페이지 (공통 기능 적용)
     */
    @GetMapping("/list")
    public ModelAndView categoryList(HttpServletRequest request) {
        logger.debug("Quiz category list page requested");

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        ModelAndView modelAndView = new ModelAndView("manage/quiz-category/list");

        try {
            // URL 파라미터에서 검색 조건 생성
            AdminListSearch searchCondition = AdminListUtils.createSearchConditionFromParameters(request.getParameterMap());

            // 기본 검색 조건 설정
            if (searchCondition.getSearchFields() == null || searchCondition.getSearchFields().isEmpty()) {
                searchCondition.setSearchFields(Arrays.asList("category_name", "description"));
            }

            // 기본 정렬을 최신순으로 설정 (가장 나중에 생성된 것이 맨 위)
            if (searchCondition.getSortField() == null || "create_date".equals(searchCondition.getSortField())) {
                searchCondition.setSortField("category_id");
                searchCondition.setSortDirection("DESC");
            }

            // 초기 데이터 조회
            AdminListResponse<QuizCategory> listResponse = quizCategoryService.findListWithConditions(searchCondition);

            // 페이지 메타 정보 설정
            modelAndView.addObject("pageTitle", "문제 카테고리 관리");
            modelAndView.addObject("pageDescription", "문제 카테고리를 관리합니다.");
            modelAndView.addObject("username", auth.getName());
            modelAndView.addObject("searchCondition", searchCondition);
            modelAndView.addObject("listResponse", listResponse);

        } catch (Exception e) {
            logger.error("Error loading quiz category list: {}", e.getMessage(), e);
            modelAndView.addObject("errorMessage", "카테고리 목록을 불러오는 중 오류가 발생했습니다.");
        }

        return modelAndView;
    }
    
    /**
     * 문제 카테고리 등록 페이지
     */
    @GetMapping("/new")
    public ModelAndView categoryForm() {
        logger.debug("Quiz category form page requested");

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        ModelAndView modelAndView = new ModelAndView("manage/quiz-category/form");

        // 페이지 메타 정보 설정
        modelAndView.addObject("pageTitle", "문제 카테고리 등록");
        modelAndView.addObject("pageDescription", "새로운 문제 카테고리를 등록합니다.");
        modelAndView.addObject("username", auth.getName());
        modelAndView.addObject("isEdit", false);

        // 부모 카테고리 선택 옵션 추가
        List<QuizCategory> parentOptions = quizCategoryService.findParentCategoryOptions(null);
        modelAndView.addObject("parentOptions", parentOptions);

        return modelAndView;
    }
    
    /**
     * 문제 카테고리 수정 페이지
     */
    @GetMapping("/{categoryId}/edit")
    public ModelAndView categoryEditForm(@PathVariable("categoryId") Long categoryId) {
        logger.debug("Quiz category edit form page requested. ID: {}", categoryId);

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        try {
            QuizCategory quizCategory = quizCategoryService.findQuizCategoryById(categoryId);

            ModelAndView modelAndView = new ModelAndView("manage/quiz-category/form");

            // 페이지 메타 정보 설정
            modelAndView.addObject("pageTitle", "문제 카테고리 수정");
            modelAndView.addObject("pageDescription", "문제 카테고리 정보를 수정합니다.");
            modelAndView.addObject("username", auth.getName());
            modelAndView.addObject("quizCategory", quizCategory);
            modelAndView.addObject("isEdit", true);

            // 부모 카테고리 선택 옵션 추가 (자기 자신과 하위 카테고리 제외)
            List<QuizCategory> parentOptions = quizCategoryService.findParentCategoryOptions(categoryId);
            modelAndView.addObject("parentOptions", parentOptions);

            return modelAndView;

        } catch (Exception e) {
            logger.error("Error loading quiz category for edit: {}", e.getMessage(), e);
            ModelAndView modelAndView = new ModelAndView("redirect:/manage/quiz-category");
            return modelAndView;
        }
    }
    
    /**
     * 문제 카테고리 등록 처리 (AJAX)
     */
    @PostMapping("/add")
    @ResponseBody
    public HashMap<String, Object> createCategory(@RequestBody QuizCategory quizCategory) {
        logger.debug("Creating quiz category: {}", quizCategory);

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        HashMap<String, Object> response = new HashMap<>();

        try {
            // 생성자 정보 설정
            quizCategory.setCreateId(auth.getName());

            quizCategoryService.createQuizCategory(quizCategory);

            response.put("success", true);
            response.put("message", "문제 카테고리가 성공적으로 등록되었습니다.");

        } catch (IllegalArgumentException e) {
            logger.warn("Invalid quiz category data: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());

        } catch (Exception e) {
            logger.error("Error creating quiz category: {}", e.getMessage(), e);

            // DB 제약조건 위반 에러 처리
            if (e.getCause() instanceof SQLIntegrityConstraintViolationException) {
                SQLIntegrityConstraintViolationException sqlEx = (SQLIntegrityConstraintViolationException) e.getCause();
                if (sqlEx.getMessage().contains("Duplicate entry") && sqlEx.getMessage().contains("UX_category_name")) {
                    response.put("success", false);
                    response.put("message", "이미 존재하는 카테고리명입니다. 다른 이름을 사용해주세요.");
                } else {
                    response.put("success", false);
                    response.put("message", "데이터베이스 제약조건 위반: " + sqlEx.getMessage());
                }
            } else {
                response.put("success", false);
                response.put("message", "카테고리 등록 중 오류가 발생했습니다: " + e.getMessage());
            }
        }

        return response;
    }
    
    /**
     * 문제 카테고리 수정 처리 (AJAX)
     */
    @PostMapping("/{categoryId}")
    @ResponseBody
    public HashMap<String, Object> updateCategory(@PathVariable("categoryId") Long categoryId, @RequestBody QuizCategory quizCategory) {
        logger.debug("Updating quiz category. ID: {}, Data: {}", categoryId, quizCategory);

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        HashMap<String, Object> response = new HashMap<>();

        try {
            // 수정자 정보 설정
            quizCategory.setCategoryId(categoryId);
            quizCategory.setLastUpdateId(auth.getName());

            quizCategoryService.updateQuizCategory(quizCategory);

            response.put("success", true);
            response.put("message", "문제 카테고리가 성공적으로 수정되었습니다.");

        } catch (IllegalArgumentException e) {
            logger.warn("Invalid quiz category data: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());

        } catch (Exception e) {
            logger.error("Error updating quiz category: {}", e.getMessage(), e);

            // DB 제약조건 위반 에러 처리
            if (e.getCause() instanceof SQLIntegrityConstraintViolationException) {
                SQLIntegrityConstraintViolationException sqlEx = (SQLIntegrityConstraintViolationException) e.getCause();
                if (sqlEx.getMessage().contains("Duplicate entry") && sqlEx.getMessage().contains("UX_category_name")) {
                    response.put("success", false);
                    response.put("message", "이미 존재하는 카테고리명입니다. 다른 이름을 사용해주세요.");
                } else {
                    response.put("success", false);
                    response.put("message", "데이터베이스 제약조건 위반: " + sqlEx.getMessage());
                }
            } else {
                response.put("success", false);
                response.put("message", "카테고리 수정 중 오류가 발생했습니다: " + e.getMessage());
            }
        }

        return response;
    }



    /**
     * 문제 카테고리 삭제 처리 (API)
     */
    @DeleteMapping("/{categoryId}")
    @ResponseBody
    public HashMap<String, Object> deleteCategory(@PathVariable("categoryId") Long categoryId) {
        logger.debug("Deleting quiz category. ID: {}", categoryId);

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        HashMap<String, Object> response = new HashMap<>();

        try {
            quizCategoryService.deleteQuizCategory(categoryId, auth.getName());

            response.put("success", true);
            response.put("message", "문제 카테고리가 성공적으로 삭제되었습니다.");

        } catch (IllegalArgumentException e) {
            logger.warn("Invalid category ID: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());

        } catch (Exception e) {
            logger.error("Error deleting quiz category: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "카테고리 삭제 중 오류가 발생했습니다.");
        }

        return response;
    }
    
    /**
     * 문제 카테고리 상세 조회 (AJAX)
     */
    @GetMapping("/{categoryId}/detail")
    @ResponseBody
    public HashMap<String, Object> getCategory(@PathVariable("categoryId") Long categoryId) {
        logger.debug("Getting quiz category. ID: {}", categoryId);

        HashMap<String, Object> response = new HashMap<>();

        try {
            QuizCategory category = quizCategoryService.findQuizCategoryById(categoryId);

            response.put("success", true);
            response.put("data", category);

        } catch (IllegalArgumentException e) {
            logger.warn("Invalid category ID: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());

        } catch (Exception e) {
            logger.error("Error getting quiz category: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "카테고리 조회 중 오류가 발생했습니다.");
        }

        return response;
    }
    
    /**
     * 문제 카테고리 목록 조회 (AJAX)
     */
    @GetMapping("/list-data")
    @ResponseBody
    public HashMap<String, Object> getCategoryList() {
        logger.debug("Getting quiz category list");

        HashMap<String, Object> response = new HashMap<>();

        try {
            List<QuizCategory> categories = quizCategoryService.findAllQuizCategories();

            response.put("success", true);
            response.put("data", categories);

        } catch (Exception e) {
            logger.error("Error getting quiz category list: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "카테고리 목록 조회 중 오류가 발생했습니다.");
        }

        return response;
    }

    /**
     * 공통 리스트 기능을 사용한 카테고리 목록 조회 (AJAX)
     */
    @PostMapping("/search")
    @ResponseBody
    public AdminListResponse<QuizCategory> searchCategories(@RequestBody AdminListSearch searchCondition) {
        logger.debug("Searching quiz categories with conditions: {}", searchCondition);

        try {
            logger.debug("Received search condition - selectedSearchField: {}, searchFields: {}, searchKeyword: {}",
                        searchCondition.getSelectedSearchField(),
                        searchCondition.getSearchFields(),
                        searchCondition.getSearchKeyword());

            // JavaScript에서 이미 searchFields를 설정해서 보내므로 별도 처리 불필요
            // 하지만 안전을 위해 기본값 설정
            if (searchCondition.getSearchFields() == null || searchCondition.getSearchFields().isEmpty()) {
                searchCondition.setSearchFields(Arrays.asList("category_name", "description"));
            }

            return quizCategoryService.findListWithConditions(searchCondition);

        } catch (Exception e) {
            logger.error("Error searching quiz categories: {}", e.getMessage(), e);
            return AdminListResponse.error("카테고리 검색 중 오류가 발생했습니다: " + e.getMessage(), "SEARCH_ERROR");
        }
    }

    /**
     * 부모 카테고리 선택 옵션 조회 (AJAX)
     */
    @GetMapping("/parent-options")
    @ResponseBody
    public HashMap<String, Object> getParentCategoryOptions(@RequestParam(value = "excludeId", required = false) Long excludeId) {
        logger.debug("Getting parent category options, excluding ID: {}", excludeId);

        HashMap<String, Object> response = new HashMap<>();

        try {
            List<QuizCategory> parentOptions = quizCategoryService.findParentCategoryOptions(excludeId);

            response.put("success", true);
            response.put("data", parentOptions);

        } catch (Exception e) {
            logger.error("Error getting parent category options: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "부모 카테고리 옵션 조회 중 오류가 발생했습니다.");
        }

        return response;
    }

    /**
     * 계층형 구조로 카테고리 목록 조회 (AJAX)
     */
    @GetMapping("/hierarchy")
    @ResponseBody
    public HashMap<String, Object> getCategoriesWithHierarchy() {
        logger.debug("Getting categories with hierarchy");

        HashMap<String, Object> response = new HashMap<>();

        try {
            List<QuizCategory> categories = quizCategoryService.findCategoriesWithHierarchy();

            response.put("success", true);
            response.put("data", categories);

        } catch (Exception e) {
            logger.error("Error getting categories with hierarchy: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "계층형 카테고리 목록 조회 중 오류가 발생했습니다.");
        }

        return response;
    }

    /**
     * 최상위 카테고리 목록 조회 (AJAX)
     */
    @GetMapping("/root-categories")
    @ResponseBody
    public HashMap<String, Object> getRootCategories() {
        logger.debug("Getting root categories");

        HashMap<String, Object> response = new HashMap<>();

        try {
            List<QuizCategory> rootCategories = quizCategoryService.findRootCategories();

            response.put("success", true);
            response.put("data", rootCategories);

        } catch (Exception e) {
            logger.error("Error getting root categories: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "최상위 카테고리 목록 조회 중 오류가 발생했습니다.");
        }

        return response;
    }

    /**
     * 특정 부모 카테고리의 자식 카테고리 목록 조회 (AJAX)
     */
    @GetMapping("/{parentId}/children")
    @ResponseBody
    public HashMap<String, Object> getChildCategories(@PathVariable("parentId") Long parentId) {
        logger.debug("Getting child categories for parent ID: {}", parentId);

        HashMap<String, Object> response = new HashMap<>();

        try {
            List<QuizCategory> childCategories = quizCategoryService.findChildCategories(parentId);

            response.put("success", true);
            response.put("data", childCategories);

        } catch (Exception e) {
            logger.error("Error getting child categories: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "자식 카테고리 목록 조회 중 오류가 발생했습니다.");
        }

        return response;
    }

    /**
     * 카테고리 경로 조회 (AJAX)
     */
    @GetMapping("/{categoryId}/path")
    @ResponseBody
    public HashMap<String, Object> getCategoryPath(@PathVariable("categoryId") Long categoryId) {
        logger.debug("Getting category path for ID: {}", categoryId);

        HashMap<String, Object> response = new HashMap<>();

        try {
            List<QuizCategory> categoryPath = quizCategoryService.findCategoryPath(categoryId);

            response.put("success", true);
            response.put("data", categoryPath);

        } catch (Exception e) {
            logger.error("Error getting category path: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "카테고리 경로 조회 중 오류가 발생했습니다.");
        }

        return response;
    }
}
